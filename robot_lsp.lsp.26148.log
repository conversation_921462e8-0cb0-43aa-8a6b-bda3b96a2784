lsp: 2025-06-20 09:13:05 UTC pid: 26148 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['--log-file=c:\\Alternative\\robot_lsp.log', '--verbose']

lsp: 2025-06-20 09:13:05 UTC pid: 26148 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

lsp: 2025-06-20 09:13:05 UTC pid: 26148 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

lsp: 2025-06-20 09:13:05 UTC pid: 26148 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkLanguageServer IO language server. pid: 26148

lsp: 2025-06-20 09:13:05 UTC pid: 26148 - MainThread - INFO - robotframework_ls.robotframework_ls_impl
Using watch implementation: watchdog (customize with ROBOTFRAMEWORK_LS_WATCH_IMPL environment variable)

lsp: 2025-06-20 09:13:05 UTC pid: 26148 - MainThread - INFO - robocorp_ls_core.remote_fs_observer_impl
Initializing Remote FS Observer with the following args: ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-u', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\vendored\\robocorp_ls_core\\remote_fs_observer__main__.py', '--log-file=c:\\Alternative\\robot_lsp.log', '-v']


*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Documentation  VMS Dashboard Validation

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Library                                             String
Library                                             OperatingSystem
Library                                             DatabaseLibrary
Library                                             ../../../common_utilities/Database_Library.py
Library                                             ../../utility/Common_Functions.py
Resource                                            ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../keywords/common/common_keywords.robot
Library                                             XML
Library                                             Collections
Library                                             Screenshot
Variables                                          ../../utility/SQLVariables.py
Library                                            DateTime

#***********************************PROJECT RESOURCES***************************************

*** Variables ***
${MAIN_LINK}                                 xpath=//*[text()[normalize-space(.)='Main']]

${ADMIN_LINK}                                xpath=//*[text()[normalize-space(.)='Admin']]
${USER_MANAGEMENT_LINK}                      xpath=//a[text()[normalize-space(.)='User Management']]
${USER_MANAGEMENT_TEXT_LABEL}                xpath=//h2[text()[normalize-space(.)='User Management']]
${ADD_NEW_USER_LINK}                         xpath=//a[@id='btnAddUser']
${SEARCH_FOR_USER_INPUT}                     xpath=//input[@id='searchField'][contains(@class,'form-control')]
${DELETE_USER_BTN}                           xpath=//a[@id='btnDelete'][contains(@class,'nav-link')][text()[normalize-space(.)='Delete']]
${UPDATE_USER_BTN}                           xpath=//a[@id='btnUpdateUser'][text()[normalize-space(.)='Update']]
${DELETE_USER_POP_UP_HEADER_TEXT}            xpath=//h4[contains(@class,'modal-title')][text()[normalize-space(.)='Admin - Delete User']]
${DELETE_USER_CONFIRMATION_TEXT}             xpath=//label[text()[normalize-space(.)='Are you sure you want to delete this User?']]
${DELETE_USER_USER_NAME_CONFIRMATION_TEXT}   xpath=//label[@id='MainContent_lblDeleteLink']
${DELETE_USER_USER_WARNING_TEXT}             xpath=//label[contains(@class,'text-warning')][text()[normalize-space(.)='This action cannot be undone.']]
${DELETE_USER_CONFIRMATION_BTN}              xpath=//input[@id='btnDeleteVendorEmail'][@type='submit']
${DELETE_USER_CANCEL_BTN}                    xpath=//div[@id='deleteUser']/descendant::input[contains(@class,'btn-default')][@type='button']
${DELETE_USER_POP_UP}                        xpath=//div[@id='deleteUser']
${USER_DELETED_MESSAGE}                      xpath=/descendant::*[@id='ConfirmMsg'][text()[normalize-space(.)='User successfully deleted.']]
${USER_DELETED_OK_BTN}                       xpath=//input[@id='MainContent_btnSubmitComplete']
${ADD_NEW_USER_POP_UP}                       xpath=//div[@id='addUserModal']
${TEST_E}                                    xpath=//div[@id='addUserModal']/descendant::form[contains(@class,'needs-validation')]/descendant::div[contains(@class,'row')][2]
${ADD_NEW_USER_POP_UP_TEXT}                  xpath=//*[contains(@class,'modal-title')][text()[normalize-space(.)='Admin - Add New User']]
${UPDATE_USER_POP_UP}                        xpath=//div[@id='editUserModal'][contains(@class,'modal')]
${UPDATE_USER_SAVE_BTN}                      xpath=//button[@id='btnUpdateCall'][@type='submit'][text()[normalize-space(.)='Update']]
${ADD_NEW_USER_USERNAME_INPUT}               xpath=//input[@id='MainContent_txtAdd_Username'][@name='ctl00$MainContent$txtAdd_Username']
${ADD_NEW_USER_NAME_INPUT}                   xpath=//input[@id='MainContent_txtAdd_Name'][@name='ctl00$MainContent$txtAdd_Name']
${ADD_NEW_USER_USERADDED_MSG}                xpath=//div[@id='ConfirmMsg'][text()[normalize-space(.)='New User successfully added.']]
${ADD_NEW_USER_USERADDED_OK_BTN}             xpath=//input[@id='MainContent_btnSubmitComplete'][@name='ctl00$MainContent$btnSubmitComplete']
${ADD_NEW_USER_BROWSE_RADIO}                 xpath=//input[@id='MainContent_rdbAdd_Browse'][@name='ctl00$MainContent$group'][@type='radio']
${ADD_NEW_USER_USER_RADIO}                   xpath=//input[@id='MainContent_rdbAdd_User'][@name='ctl00$MainContent$group'][@type='radio']
${ADD_NEW_USER_SUPERVISOR_RADIO}             xpath=//input[@id='MainContent_rdbAdd_Supervisor'][@name='ctl00$MainContent$group']
${ADD_NEW_USER_ADMINISTRATOR_RADIO}          xpath=//input[@id='MainContent_rdbAdd_Admin'][@name='ctl00$MainContent$group']
${EDIT_USER_BROWSE_RADIO}                    xpath=//input[@id='MainContent_rdbEdit_Browse'][@name='ctl00$MainContent$group'][@type='radio']
${EDIT_USER_USER_RADIO}                      xpath=//input[@id='MainContent_rdbEdit_User'][@name='ctl00$MainContent$group'][@type='radio']
${EDIT_USER_SUPERVISOR_RADIO}                xpath=//input[@id='MainContent_rdbEdit_Supervisor'][@name='ctl00$MainContent$group'][@type='radio']
${EDIT_USER_ADMINISTRATOR_RADIO}             xpath=//input[@id='MainContent_rdbAdmin'][@name='ctl00$MainContent$group'][@type='radio']

${ADD_NEW_USER_SUBMIT_BTN}                   xpath=//button[@id='btnAdd_VendorEmail'][text()[normalize-space(.)='Submit']]
${ADD_NEW_USER_CANCEL_BTN}                   xpath=//*[@id='addUserModal']/div/div/form/div[contains(@class,'modal-footer')]/div/div[contains(@class,'col-md-4')][contains(@class,'form-group')]/input
${VMS_USERS_TABLE}                           xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]
${ROWS_PER_PAGE_FILTER}                      xpath=//select[@id='changeRows'][@name='rowsPerPage']
${ROW_NUMBERS_DISPLAYED}                     xpath=//div[contains(@class,'gs-pagination')]/descendant::div[contains(@class,'col-md-6')]/descendant::span[contains(text(), 'Showing')]
${PREVIOUS_PAGE_BUTTON}                      xpath=//button[contains(@class,'btn-default')]/span[text()[normalize-space(.)='Prev']]
${NEXT_PAGE_BUTTON}                          xpath=//button[contains(@class,'btn-default')]/descendant::*[text()[normalize-space(.)='Next']]
${USER_MANAGEMENT_BUTTONS_DIV}               xpath=//div[contains(@class,'justify-content-end')]

*** Variables ***
${STRING_LIST}    ['1', '5', '10', '15']
*** Keywords ***

The user Adds a new VMS User
    [Arguments]     ${USER_NAME}    ${NAME}    ${USER_ROLE}

    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' Page is displayed
    SeleniumLibrary.Click Element    ${ADD_NEW_USER_LINK}
    Sleep   2s
    SeleniumLibrary.Element Should Be Visible    ${ADD_NEW_USER_POP_UP_TEXT}     #Verify that the 'Add New User' POP-UP is displayed


    #Populate the user details
    SeleniumLibrary.Input Text    ${ADD_NEW_USER_USERNAME_INPUT}    ${USER_NAME}
    SeleniumLibrary.Input Text    ${ADD_NEW_USER_NAME_INPUT}    ${NAME}

    #Select the ROLE
    ${role}=  Convert To Uppercase  ${USER_ROLE}
     IF  "${role}" == "BROWSE"
        SeleniumLibrary.Click Element      ${ADD_NEW_USER_BROWSE_RADIO}
     ELSE IF  "${role}" == "USER"
        SeleniumLibrary.Click Element      ${ADD_NEW_USER_USER_RADIO}
     ELSE IF  "${role}" == "SUPERVISOR"
        SeleniumLibrary.Click Element      ${ADD_NEW_USER_SUPERVISOR_RADIO}
     ELSE IF  "${role}" == "ADMINISTRATOR"
        SeleniumLibrary.Click Element      ${ADD_NEW_USER_ADMINISTRATOR_RADIO}
     ELSE
        SeleniumLibrary.Click Element      ${ADD_NEW_USER_BROWSE_RADIO}
     END
    
    
    capture page screenshot     NEW_USER_DETAILS_POPULATED.png

    SeleniumLibrary.Click Element    ${ADD_NEW_USER_SUBMIT_BTN}

    #Verify the User Added confirmation message
    Sleep   5s
    SeleniumLibrary.Element Should Be Visible    ${ADD_NEW_USER_USERADDED_MSG}     #Verify that the 'Add New User' POP-UP is displayed
    ${user_added_msg}=      SeleniumLibrary.Get Text    ${ADD_NEW_USER_USERADDED_MSG}
    Should Be Equal As Strings    ${user_added_msg}    New User successfully added.
    capture page screenshot     NEW_USER_ADDED_CONFIRMATION.png
    SeleniumLibrary.Click Element      ${ADD_NEW_USER_USERADDED_OK_BTN}
    Sleep   5s



The created user must be found on VMS Application and Database
    [Arguments]     ${USER_NAME}    ${NAME}     ${USER_ROLE}

    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' Page is displayed
    SeleniumLibrary.Element Should Be Visible    ${VMS_USERS_TABLE}     #Verify that the 'VMS Added Users' Table is displayed

    #Build the table's body element
    ${table_tbody}=     Catenate    ${VMS_USERS_TABLE}     /tbody[contains(@class,'gs-table-body')]

    ${table_rows_element}=          Catenate    ${table_tbody}/tr[1]
    ${user_name_element}=          Catenate    ${table_rows_element}/td[1]
    ${name_element}=          Catenate    ${table_rows_element}/td[2]
    ${user_name_text}=          SeleniumLibrary.Get Text    ${user_name_element}
    ${name_text}=          SeleniumLibrary.Get Text    ${name_element}

    #Verify the user details are the same as expected
    ${USER_NAME}=  Convert To Lower Case   ${USER_NAME}
    Should Be Equal As Strings    ${user_name_text}    ${USER_NAME}
    Should Be Equal As Strings    ${name_text}    ${NAME}

    capture page screenshot     ${USER_NAME}.png

    #Verify that the user exists in the database
    ${DB_Data}=     Get the VMS User details from the database      ${USER_NAME}
    ${db_Username_data_to_verify}=    Get From Dictionary    ${DB_Data}    Username
    ${db_Name_data_to_verify}=    Get From Dictionary    ${DB_Data}    Name
    ${db_Access_data_to_verify}=    Get From Dictionary    ${DB_Data}    Access
    
    IF    '${USER_ROLE.strip()}' == 'Supervisor'
        ${USER_ROLE}=     Set Variable      Supervisory-Main
    ELSE
        IF    '${USER_ROLE.strip()}' == 'User'
            ${USER_ROLE}=     Set Variable      User-Main
        END
    END

    common_keywords.Verify if values are equal    ${USER_NAME.strip()}    ${db_Username_data_to_verify.strip()}
    common_keywords.Verify if values are equal    ${NAME.strip()}     ${db_Name_data_to_verify.strip()}
    common_keywords.Verify if values are equal    ${USER_ROLE.strip()}    ${db_Access_data_to_verify.strip()}

    #Verify that the user is enabled for login on VMS
    ${is_user_enabled}=    Get From Dictionary    ${DB_Data}    Enabled
    common_keywords.Verify if values are equal    True    ${is_user_enabled}



The user navigates to Admin - User Management


    SeleniumLibrary.Page Should Contain     Dashboard   #Verify that the landing page is Dashboard
    capture page screenshot     VMS_Landing_Page.png

    #Navigate to User Creation
    SeleniumLibrary.Click Element    ${ADMIN_LINK}
    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' link is visible
    SeleniumLibrary.Click Element    ${USER_MANAGEMENT_LINK}
    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' Page is displayed



Updates the VMS User's role
    [Arguments]     ${USER_NAME}     ${USER_ROLE}
    ${radio_button_is_checked}=    Set Variable

    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' Page is displayed
    #Build the table's body element
    ${table_tbody}=     Catenate    ${VMS_USERS_TABLE}     /tbody[contains(@class,'gs-table-body')]

    ${table_rows_element}=          Catenate    ${table_tbody}/tr[1]
    ${user_name_element}=          Catenate    ${table_rows_element}/td[1]
    ${name_element}=          Catenate    ${table_rows_element}/td[2]

    ${user_name_text}=          SeleniumLibrary.Get Text    ${user_name_element}

    #Verify the user details are the same as expected
     ${USER_NAME}=  Convert To Lower Case   ${USER_NAME}
     Should Be Equal As Strings    ${user_name_text}    ${USER_NAME}
     capture page screenshot     ${USER_NAME}.png

     #Update the user details
     SeleniumLibrary.Click Element    ${UPDATE_USER_BTN}
     Sleep   5s
     SeleniumLibrary.Element Should Be Visible    ${UPDATE_USER_POP_UP}     #Verify that the 'Update User' POP-UP is displayed
     capture page screenshot     Update_User_Pop_Up.png


     #Verify that the new role that must be selected is not alreay assigned to the user
     ${role}=  Convert To Uppercase  ${USER_ROLE}
     IF  "${role}" == "BROWSE"
        ${radio_button_is_checked}=   SeleniumLibrary.Get Element Attribute    ${EDIT_USER_BROWSE_RADIO}    checked
        Run Keyword If    '${radio_button_is_checked}' != 'true'
        ...    SeleniumLibrary.Click Element      ${EDIT_USER_BROWSE_RADIO}
        ...  ELSE
        ...    Run Keyword And Warn On Failure   Fail     'BROWSE' radio button is already selected, hence the user details were not updated
     ELSE IF  "${role}" == "USER"
        ${radio_button_is_checked}=   SeleniumLibrary.Get Element Attribute    ${EDIT_USER_USER_RADIO}    checked
        #${radio_button_not_selected}=         Run Keyword And Return Status    Element Should Not Be Selected    ${EDIT_USER_USER_RADIO}
        Run Keyword If    '${radio_button_is_checked}' != 'true'
        ...    SeleniumLibrary.Click Element      ${EDIT_USER_USER_RADIO}
        ...  ELSE
        ...    Run Keyword And Warn On Failure   Fail     'USER' radio button is already selected, hence the user details were not updated
     ELSE IF  "${role}" == "SUPERVISOR"
        ${radio_button_is_checked}=   SeleniumLibrary.Get Element Attribute    ${EDIT_USER_SUPERVISOR_RADIO}    checked
        Run Keyword If    '${radio_button_is_checked}' != 'true'
        ...    SeleniumLibrary.Click Element      ${EDIT_USER_SUPERVISOR_RADIO}
        ...  ELSE
        ...    Run Keyword And Warn On Failure   Fail     'SUPERVISOR' radio button is already selected, hence the user details were not updated
     ELSE IF  "${role}" == "ADMINISTRATOR"
        ${radio_button_is_checked}=   SeleniumLibrary.Get Element Attribute    ${EDIT_USER_ADMINISTRATOR_RADIO}    checked
        Run Keyword If    '${radio_button_is_checked}' != 'true'
        ...    SeleniumLibrary.Click Element      ${EDIT_USER_ADMINISTRATOR_RADIO}
        ...  ELSE
        ...    Run Keyword And Warn On Failure   Fail     'ADMINISTRATOR' radio button is already selected, hence the user details were not updated
     END

     capture page screenshot      Updated_User_Details.png
     SeleniumLibrary.Click Element    ${UPDATE_USER_SAVE_BTN}
     IF    '${radio_button_is_checked}' == 'true'
          Fail  Test execution failed
     END


Searches for existing user
    [Arguments]     ${USER_NAME}


    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' Page is displayed

    Sleep   1s
    SeleniumLibrary.Input Text    ${SEARCH_FOR_USER_INPUT}    ${USER_NAME}

    Sleep   2s
    SeleniumLibrary.Element Should Be Visible    ${VMS_USERS_TABLE}     #Verify that the 'VMS Added Users' Table is displayed

    #Build the table's body element
    ${table_tbody}=     Catenate    ${VMS_USERS_TABLE}     /tbody[contains(@class,'gs-table-body')]

    #Check if the user name is returned on the results
    ${IsElementVisible}=  Run Keyword And Return Status    Element Should Be Visible   ${table_tbody}

    IF    ${IsElementVisible} == $True
        Log     The User: '${USER_NAME}' was returned on the search results, which means it exists on VMS.
    ELSE
        Fail    The User: '${USER_NAME}' was not returned on the search results, which means it does not exist on VMS
    END

    capture page screenshot     User_Search_${USER_NAME}.png

The user's role must be updated with the expected role
    [Arguments]     ${USER_NAME}     ${USER_ROLE}
    ${radio_button_is_checked}=    Set Variable

    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' Page is displayed
    SeleniumLibrary.Element Should Be Visible    ${VMS_USERS_TABLE}     #Verify that the 'VMS Added Users' Table is displayed

    #Build the table's body element
    ${table_tbody}=     Catenate    ${VMS_USERS_TABLE}     /tbody[contains(@class,'gs-table-body')]

    ${table_rows_element}=          Catenate    ${table_tbody}/tr[1]
    ${user_name_element}=          Catenate    ${table_rows_element}/td[1]
    ${name_element}=          Catenate    ${table_rows_element}/td[2]

    ${user_name_text}=          SeleniumLibrary.Get Text    ${user_name_element}

    #Verify the user details are the same as expected
    ${USER_NAME}=  Convert To Lower Case   ${USER_NAME}
    Should Be Equal As Strings    ${user_name_text}    ${USER_NAME}
    capture page screenshot     ${USER_NAME}.png

     #Update the user details
     SeleniumLibrary.Click Element    ${UPDATE_USER_BTN}
     Sleep   2s
     SeleniumLibrary.Element Should Be Visible    ${UPDATE_USER_POP_UP}     #Verify that the 'Add New User' POP-UP is displayed
     capture page screenshot     Update_User_Pop_Up.png


     #Verify that the new role has been added to the user
     ${role}=  Convert To Uppercase  ${USER_ROLE}
     IF  "${role}" == "BROWSE"
        ${radio_button_is_checked}=   SeleniumLibrary.Get Element Attribute    ${EDIT_USER_BROWSE_RADIO}    checked
        Run Keyword If    '${radio_button_is_checked}' == 'true'
        ...    Log   The user: '${USER_NAME}' was successfully updated to have the '${USER_ROLE}' role!
        ...  ELSE
        ...    Run Keyword And Warn On Failure   Fail     The user: '${USER_NAME}' was not updated to have the '${USER_ROLE}' role!
     ELSE IF  "${role}" == "USER"
        ${radio_button_is_checked}=   SeleniumLibrary.Get Element Attribute    ${EDIT_USER_USER_RADIO}    checked
        Run Keyword If    '${radio_button_is_checked}' == 'true'
        ...    Log   The user: '${USER_NAME}' was successfully updated to have the '${USER_ROLE}' role!
        ...  ELSE
        ...    Run Keyword And Warn On Failure   Fail     The user: '${USER_NAME}' was not updated to have the '${USER_ROLE}' role!
     ELSE IF  "${role}" == "SUPERVISOR"
        ${radio_button_is_checked}=   SeleniumLibrary.Get Element Attribute    ${EDIT_USER_SUPERVISOR_RADIO}    checked
        Run Keyword If    '${radio_button_is_checked}' == 'true'
        ...    Log   The user: '${USER_NAME}' was successfully updated to have the '${USER_ROLE}' role!
        ...  ELSE
        ...    Run Keyword And Warn On Failure   Fail     The user: '${USER_NAME}' was not updated to have the '${USER_ROLE}' role!
     ELSE IF  "${role}" == "ADMINISTRATOR"
        ${radio_button_is_checked}=   SeleniumLibrary.Get Element Attribute    ${EDIT_USER_ADMINISTRATOR_RADIO}    checked
        Run Keyword If    '${radio_button_is_checked}' == 'true'
        ...    Log   The user: '${USER_NAME}' was successfully updated to have the '${USER_ROLE}' role!
        ...  ELSE
        ...    Run Keyword And Warn On Failure   Fail     The user: '${USER_NAME}' was not updated to have the '${USER_ROLE}' role!
     END

     capture page screenshot      Updated_User_Details.png
     SeleniumLibrary.Click Element    ${UPDATE_USER_SAVE_BTN}
     IF    '${radio_button_is_checked}' != 'true'
          Fail  Test execution failed
     END

     #Verify that the user exists in the database
    ${DB_Data}=     Get the VMS User details from the database      ${USER_NAME}
    ${db_Username_data_to_verify}=    Get From Dictionary    ${DB_Data}    Username
    ${db_Name_data_to_verify}=    Get From Dictionary    ${DB_Data}    Name
    ${db_Access_data_to_verify}=    Get From Dictionary    ${DB_Data}    Access

    IF    '${USER_ROLE.strip()}' == 'Supervisor'
        ${USER_ROLE}=     Set Variable      Supervisory-Main
    ELSE
        IF    '${USER_ROLE.strip()}' == 'User'
            ${USER_ROLE}=     Set Variable      User-Main
        END
    END

    common_keywords.Verify if values are equal    ${USER_NAME.strip()}    ${db_Username_data_to_verify.strip()}
    common_keywords.Verify if values are equal    ${USER_ROLE.strip()}    ${db_Access_data_to_verify.strip()}

    #Verify that the user is enabled for login on VMS
    ${is_user_enabled}=    Get From Dictionary    ${DB_Data}    Enabled
    common_keywords.Verify if values are equal    True    ${is_user_enabled}

Deletes the user from the VMS Application
    [Arguments]     ${USER_NAME}

    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' Page is displayed

    SeleniumLibrary.Element Should Be Visible    ${VMS_USERS_TABLE}     #Verify that the 'VMS Added Users' Table is displayed

    #Build the table's body element
    ${table_tbody}=     Catenate    ${VMS_USERS_TABLE}     /tbody[contains(@class,'gs-table-body')]

    ${table_rows_element}=          Catenate    ${table_tbody}/tr[1]
    ${user_name_element}=          Catenate    ${table_rows_element}/td[1]
    ${name_element}=          Catenate    ${table_rows_element}/td[2]

    ${user_name_text}=          SeleniumLibrary.Get Text    ${user_name_element}

    #Verify the user details are the same as expected
    ${USER_NAME}=  Convert To Lower Case   ${USER_NAME}
    Should Be Equal As Strings    ${user_name_text}    ${USER_NAME}
    capture page screenshot     ${USER_NAME}.png

     #Delete the user
     SeleniumLibrary.Click Element    ${DELETE_USER_BTN}
     Sleep   1s
     SeleniumLibrary.Element Should Be Visible    ${DELETE_USER_POP_UP}     #Verify that the 'Admin - Delete User' POP-UP is displayed


     #Verify the details displayed on the pop-up before deleting the user
     ${delete_user_pop_up_header_message}=      SeleniumLibrary.Get Text    ${DELETE_USER_POP_UP_HEADER_TEXT}
     ${user_name_to_be_deleted}=                SeleniumLibrary.Get Text    ${DELETE_USER_USER_NAME_CONFIRMATION_TEXT}
     ${delete_user_confirmation_message}=       SeleniumLibrary.Get Text    ${DELETE_USER_CONFIRMATION_TEXT}
     ${delete_user_warning_message}=            SeleniumLibrary.Get Text    ${DELETE_USER_USER_WARNING_TEXT}

     ${header_verification_status}=                       Run Keyword And Return Status    common_keywords.Verify if values are equal     Admin - Delete User     ${delete_user_pop_up_header_message}
     ${user_name_verification_status}=                    Run Keyword And Return Status    common_keywords.Verify if values are equal     ${USER_NAME}     ${user_name_to_be_deleted}
     ${delete_user_confirmation_verification_status}=     Run Keyword And Return Status    common_keywords.Verify if values are equal     Are you sure you want to delete this User?     ${delete_user_confirmation_message}
     ${delete_user_warning_verification_status}=          Run Keyword And Return Status    common_keywords.Verify if values are equal     This action cannot be undone.     ${delete_user_warning_message}

     #Assert that all the verifications passed, if this is the case delete the user. Otherwise click the cancel button
     ${header_verification_status}=                       Convert To Boolean    ${header_verification_status}
     ${user_name_verification_status}=                    Convert To Boolean    ${user_name_verification_status}
     ${delete_user_confirmation_verification_status}=     Convert To Boolean    ${delete_user_confirmation_verification_status}
     ${delete_user_warning_verification_status}=          Convert To Boolean    ${delete_user_warning_verification_status}

     ${all_verifications_passed} =    All Variables are True     ${header_verification_status}    ${user_name_verification_status}    ${delete_user_confirmation_verification_status}     ${delete_user_warning_verification_status}

     capture page screenshot     Delete_User_Pop_Up.png

     Run Keyword If    ${all_verifications_passed}
     ...    SeleniumLibrary.Click Element      ${DELETE_USER_CONFIRMATION_BTN}
     ...  ELSE
     ...    SeleniumLibrary.Click Element      ${DELETE_USER_CANCEL_BTN}

     Sleep    3s

     Run Keyword If    not ${all_verifications_passed}    Fail    One of the Test verifications failed, hence the user could not be deleted! Please refer to the report.
     ...    ELSE
     ...    SeleniumLibrary.Element Should Be Visible    ${USER_DELETED_MESSAGE}

     IF    ${all_verifications_passed}
          capture page screenshot     Delete_User_Confirmation_Pop_Up.png
          SeleniumLibrary.Click Element      ${USER_DELETED_OK_BTN}
     END

The user should not be found when you are searching for it on VMS
    [Arguments]     ${USER_NAME}

    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' Page is displayed
    SeleniumLibrary.Input Text    ${SEARCH_FOR_USER_INPUT}    ${USER_NAME}

    Sleep   1s
    SeleniumLibrary.Element Should Be Visible    ${VMS_USERS_TABLE}     #Verify that the 'VMS Added Users' Table is displayed

    #Build the table's body element
    ${table_tbody}=     Catenate    ${VMS_USERS_TABLE}     /tbody[contains(@class,'gs-table-body')]

    #Check if the user name is returned on the results
    ${IsElementVisible}=  Run Keyword And Return Status    Element Should Not Be Visible   ${table_tbody}

    IF    ${IsElementVisible} == $True
        Log     The User: '${USER_NAME}' was not returned on the search results, which means it has been deleted successfully.
    ELSE

        Fail    The User: '${USER_NAME}' was not deleted, it still exists on VMS on the search results
    END

    capture page screenshot     Deleted_User_Search_${USER_NAME}.png

    #Verify that the deleted user does not exist in the database
    ${DB_Data}=     Get the VMS User details from the database      ${USER_NAME}
    ${is_user_enabled}=    Get From Dictionary    ${DB_Data}    Enabled
    common_keywords.Verify if values are equal    False    ${is_user_enabled}




The user Adds a new VMS User while leaving some fields blank
    [Arguments]     ${USER_NAME}    ${NAME}    ${USER_ROLE}

    SeleniumLibrary.Page Should Contain     User Management   #Verify that the 'User Management' Page is displayed
    SeleniumLibrary.Click Element    ${ADD_NEW_USER_LINK}
    Sleep   2s
    SeleniumLibrary.Element Should Be Visible    ${ADD_NEW_USER_POP_UP_TEXT}     #Verify that the 'Add New User' POP-UP is displayed


    #Populate the user details
    SeleniumLibrary.Input Text    ${ADD_NEW_USER_USERNAME_INPUT}    ${USER_NAME}
    SeleniumLibrary.Input Text    ${ADD_NEW_USER_NAME_INPUT}    ${NAME}

    #Select the ROLE
    ${role}=  Convert To Uppercase  ${USER_ROLE}
     IF  "${role}" == "BROWSE"
        SeleniumLibrary.Click Element      ${ADD_NEW_USER_BROWSE_RADIO}
     ELSE IF  "${role}" == "USER"
        SeleniumLibrary.Click Element      ${ADD_NEW_USER_USER_RADIO}
     ELSE IF  "${role}" == "SUPERVISOR"
        SeleniumLibrary.Click Element      ${ADD_NEW_USER_SUPERVISOR_RADIO}
     ELSE IF  "${role}" == "ADMINISTRATOR"
        SeleniumLibrary.Click Element      ${ADD_NEW_USER_ADMINISTRATOR_RADIO}
     END
    SeleniumLibrary.Click Element    ${ADD_NEW_USER_SUBMIT_BTN}

The exepcted error message must be displayed
    [Arguments]     ${EXPECTED_ERROR}

    ${validation_message}=          Retrieve Validation Message from the Browser Using Java Script
    Log    ${validation_message}

    capture page screenshot     NEW_USER_DETAILS_POPULATED.png

    SeleniumLibrary.Click Element    ${ADD_NEW_USER_CANCEL_BTN}

     #Verify that the error message was displayed
    Should Be Equal    ${validation_message}    ${EXPECTED_ERROR}


The user filters the number of users to be displayed using rows per page filter
    [Arguments]     ${ROWS_TO_SELECT}
    #Verify that a provided variable is an integer
    ${integer_validation}=       Common_Functions.Validate Integer    ${ROWS_TO_SELECT}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be an integer. The valid values are 1, 5, 10 and 15.

    #Verify that the provided number falls within the list of expected numbers that are used for filtering
    ${is_in_list}=    Common_Functions.Check element in list     ${STRING_LIST}      ${ROWS_TO_SELECT}
    Run Keyword If    ${is_in_list} == ${False}    Fail    The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be amongst 1, 5, 10 and 15.

    #Select the option from the filter list
    Select From List By Value    ${ROWS_PER_PAGE_FILTER}    ${ROWS_TO_SELECT}
    capture page screenshot     USER_FILTER_${ROWS_TO_SELECT}.png

The number of rows returned on the page must be the same as the number that was used for filtering
    [Arguments]     ${ROWS_TO_SELECT}

    #Verify that the results table returned the correct results as per the filetering
    SeleniumLibrary.Element Should Be Visible    ${VMS_USERS_TABLE}     #Verify that the 'VMS Added Users' Table is displayed

    #Build the table's body element
    ${table_tbody_element}=                 Catenate    ${VMS_USERS_TABLE}/tbody[contains(@class,'gs-table-body')]
    ${table_rows_element}=          Catenate    ${table_tbody_element}/tr
    ${rows}=    Get WebElements    ${table_rows_element}
    ${table_rows_count}=    Get Length      ${rows}
    #Verify that the returned number of Rows is the same as expected
    common_keywords.Verify if values are equal      ${ROWS_TO_SELECT}       ${table_rows_count}

    #Verify that the number of last item displayed on the page is the same as the number used for the filter used
    ${page_number_text}=            SeleniumLibrary.Get Text        ${ROW_NUMBERS_DISPLAYED}
    ${substring} =    String.Get Substring    ${page_number_text}    13
    ${rows_variable_digits_length}=    Get Length     ${ROWS_TO_SELECT}
    IF    ${rows_variable_digits_length} == 1
         ${substring2}=    Get Substring    ${substring}    0    2
    ELSE
         ${substring2}=    Get Substring    ${substring}    0    3
    END

     # Trim the whitespace
    ${trimmed_string}=    Set Variable    ${substring2.strip()}
    common_keywords.Verify if values are equal      ${ROWS_TO_SELECT}       ${trimmed_string}
    Scroll Element Into View        ${ROW_NUMBERS_DISPLAYED}
    capture page screenshot     USER_FILTER_${ROWS_TO_SELECT}_VERIFICATION.png

The user reads all values from a certain column of User Management Page, then value read must correspond to the database
    [Arguments]     ${COLUMN_TO_READ}

    #Verify that the column name provided us valid
    ${valid_values}=    Create List  USERID     USER ID     USERNAME    USER NAME    LASTLOGIN   LAST LOGIN
    ${is_valid}=    Evaluate    '${COLUMN_TO_READ}'.upper() in ${valid_values}
    Run Keyword If    ${is_valid} == ${False}
    ...    Fail     The column name to search for that you provided is invalid for this test. The valid column names are: UserId, Username, Lastlogin and Last login.

    ${col_name}=    Evaluate    '${COLUMN_TO_READ}'.upper()
    ${condition1}=   Evaluate    '${col_name}' == 'LASTLOGIN'
    ${condition2}=   Evaluate    '${col_name}' == 'LAST LOGIN'
    ${condition3}=   Evaluate    '${col_name}' == 'USERID'
    ${condition4}=   Evaluate    '${col_name}' == 'USER ID'
    ${condition5}=   Evaluate    '${col_name}' == 'USERNAME'
    ${condition6}=   Evaluate    '${col_name}' == 'USER NAME'

    Run Keywords
    ...    Run Keyword If    ${condition1} or ${condition2}    Set cell Index   3
    ...  AND
    ...    Run Keyword If    ${condition3} or ${condition4}    Set cell Index   1
    ...  AND
    ...    Run Keyword If    ${condition5} or ${condition6}    Set cell Index   2

    Log    Cell index is: ${CELL_INDEX}

    #Loop through all users displayed on the page, read the contents of the column name chosen for the scenario
    #and verify its contents against the database

     SeleniumLibrary.Element Should Be Visible    ${USER_MANAGEMENT_BUTTONS_DIV}

    ${button_elements_locator}=         Catenate    ${USER_MANAGEMENT_BUTTONS_DIV}/button

    #Get all buttons that are currently displayed on the page
    ${button_elements}=     SeleniumLibrary.Get WebElements    ${button_elements_locator}
    ${total_number_of_buttons}=    Get Length    ${button_elements}

    #Verify that the previous page button is disabled when the user lands on the page
    ${previous_page_element}=   Set Variable    ${button_elements[0]}
    ${prev_page_text}=     SeleniumLibrary.Get Text    ${previous_page_element}
    ${prev_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${previous_page_element}    disabled

    Should Be Equal As Strings    ${prev_page_text}    Prev
    Should Be Equal As Strings    ${prev_page_disabled_status}    true

    #Verify that the next page is not disabled when the user lands on the page
    ${next_page_element}=    Get From List    ${button_elements}    -1
    ${next_page_text}=     SeleniumLibrary.Get Text    ${next_page_element}
    ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${next_page_element}    disabled

    Should Be Equal As Strings    ${next_page_text}    Next
    Should Be Equal As Strings    ${next_page_disabled_status}    None

    #Click the 'Next' button and verify that the new page is displayed each time the button is clicked.
     #This process must repeat until you reach the last page.
    capture page screenshot     USER_DETAILS_LANDING_PAGE.png
    WHILE    '${next_page_disabled_status}' == 'None'

        ${table_tbody_element}=    Catenate    ${VMS_USERS_TABLE}/tbody[contains(@class,'gs-table-body')]
        ${table_rows_element}=     Catenate    ${table_tbody_element}/tr
        #${rows}=    Get WebElements    ${table_rows_element}
        ${rows}=    SeleniumLibrary.Get Element Count    ${table_rows_element}  #Get the number of rows from the APC calendar

        #Loop through all rows of the current page
        FOR   ${row_num}    IN RANGE    1    ${rows+1}
            #Get the column details of the column name to be verified
            ${curr_row_element_path} =   Catenate    ${table_rows_element}   [${row_num}]
            ${col_element_path} =   Catenate    ${curr_row_element_path}       /td
            ${cols}=    SeleniumLibrary.Get Element Count    ${col_element_path}  #Get the number of columns from the current row
            ${required_col_element_path} =   Catenate    ${col_element_path}   [${CELL_INDEX}]

            ${required_col_name_text}=     SeleniumLibrary.Get Text    ${required_col_element_path}

            ${username_col_element_path} =   Catenate    ${col_element_path}   [1]
            ${username_col_text}=     SeleniumLibrary.Get Text    ${username_col_element_path}


            ${result1}=    Run Keyword And Return Status    Should Not Be Empty    ${required_col_name_text.strip()}
            ${condition1}=    Set Variable    ${result1}
            ${result2}=    Run Keyword And Return Status    Should Not Be Empty    ${username_col_text.strip()}
            ${condition2}=    Set Variable    ${result2}

            ${current_data_must_be_verified} =    All Variables are True     ${condition1}    ${condition2}

            #If the current row details are not blank then verify data against the DB
            IF    ${current_data_must_be_verified} == $True

               ${DB_Data}=     Get the VMS User details from the database      ${username_col_text}
               ${db_col_data_to_verify}=    Get From Dictionary    ${DB_Data}    ${DB_CON_NAME}
               #Format the DB time to be the same as the front end time
               IF    '${DB_CON_NAME}' == 'LastLogin'
                     ${db_col_data_to_verify}=      Format Timestamp     ${db_col_data_to_verify}
               END
               common_keywords.Verify if values are equal    ${required_col_name_text.strip()}    ${db_col_data_to_verify.strip()}

            END

        END

        ${button_elements_locator}=         Catenate    ${USER_MANAGEMENT_BUTTONS_DIV}/button
        #Get all buttons that are currently displayed on the page
        ${button_elements}=     SeleniumLibrary.Get WebElements    ${button_elements_locator}
        ${next_page_element}=    Get From List    ${button_elements}    -1
        ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${next_page_element}    disabled

        IF    '${next_page_disabled_status}' == 'None'
             #Click on the 'Next' button
             SeleniumLibrary.Click Element    ${next_page_element}
             capture page screenshot     USER_DETAILS_NEXT_PAGE.png
        END
    END


Set cell Index
    [Arguments]     ${INDEX}

    Set Global Variable    ${CELL_INDEX}        ${INDEX}
    Run Keyword If    ${INDEX} == 1
    ...    Set Global Variable    ${DB_CON_NAME}        Username
    ...  ELSE IF    ${INDEX} == 2
    ...    Set Global Variable    ${DB_CON_NAME}        Name
    ...  ELSE
    ...    Set Global Variable    ${DB_CON_NAME}        LastLogin


Format Timestamp
    [Arguments]     ${TIMESTAMP}
    ${time}=    Set Variable    ${TIMESTAMP.strip()}
    ${formatted_timestamp}=    Format DB Timestamp    ${time}
    RETURN    ${formatted_timestamp}


The user clicks the 'Next' button then next page must be displayed

    SeleniumLibrary.Element Should Be Visible    ${USER_MANAGEMENT_BUTTONS_DIV}

    ${button_elements_locator}=         Catenate    ${USER_MANAGEMENT_BUTTONS_DIV}/button

    #Get all buttons that are currently displayed on the page
    ${button_elements}=     SeleniumLibrary.Get WebElements    ${button_elements_locator}
    ${total_number_of_buttons}=    Get Length    ${button_elements}

    #Verify that the previous page button is disabled when the user lands on the page
    ${previous_page_element}=   Set Variable    ${button_elements[0]}
    ${prev_page_text}=     SeleniumLibrary.Get Text    ${previous_page_element}
    ${prev_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${previous_page_element}    disabled
    
    Should Be Equal As Strings    ${prev_page_text}    Prev
    Should Be Equal As Strings    ${prev_page_disabled_status}    true

    #Verify that the next page is not disabled when the user lands on the page
    ${next_page_element}=    Get From List    ${button_elements}    -1
    ${next_page_text}=     SeleniumLibrary.Get Text    ${next_page_element}
    ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${next_page_element}    disabled

    Should Be Equal As Strings    ${next_page_text}    Next
    Should Be Equal As Strings    ${next_page_disabled_status}    None

    #Click the 'Next' button and verify that the new page is displayed each ti,e the button is clicked.
     #This process must repeat until you reach the last page.

    WHILE    '${next_page_disabled_status}' == 'None'
        ${next_page_disabled_status} =          Verify the details on User Management Page      ${next_page_element}

        ${button_elements_locator}=         Catenate    ${USER_MANAGEMENT_BUTTONS_DIV}/button
        #Get all buttons that are currently displayed on the page
        ${button_elements}=     SeleniumLibrary.Get WebElements    ${button_elements_locator}
        ${next_page_element}=    Get From List    ${button_elements}    -1

    END

    #Cater for the last page verification

Verify the details on User Management Page
    [Arguments]     ${next_page_element}

        #Get the current page's details
        ${page_number_text}=                   SeleniumLibrary.Get Text        ${ROW_NUMBERS_DISPLAYED}
        ${starting_record_number_on_page} =    String.Get Substring    ${page_number_text}    8      11
        ${trimmed_starting_record_number}=     Set Variable    ${starting_record_number_on_page.strip()}
        ${ending_record_number_on_page} =      String.Get Substring    ${page_number_text}    13      18
        ${trimmed_ending_record_number}=       Set Variable    ${ending_record_number_on_page.strip()}
        ${total_number_of_records} =      String.Get Substring    ${page_number_text}    17      25
        ${trimmed_total_number_of_records}=       Set Variable    ${total_number_of_records.strip()}

        Log    ${page_number_text}
        #Log    ${trimmed_starting_record_number}
        #Log    ${trimmed_ending_record_number}
        #Log    ${trimmed_total_number_of_records}

        ${starting_record_array}=           Split String    ${trimmed_starting_record_number}       ${SPACE}
        ${starting_record_array_length}=    Get Length    ${starting_record_array}
        ${ending_record_array}=             Split String    ${trimmed_ending_record_number}       ${SPACE}
        ${ending_record_array_length}=      Get Length    ${ending_record_array}

        #Get the actual record numbers
        ${actual_start_record_data}=    Set Variable    ${starting_record_array[0]}
        ${actual_end_record_data}=      Set Variable
        FOR    ${ending_record_data}    IN    @{ending_record_array}
            ${integer_validation}=       Common_Functions.Validate Integer    ${ending_record_data}
            IF    '${integer_validation}' == 'valid integer'
                 ${actual_end_record_data}=    Set Variable    ${ending_record_data}
                 Exit For Loop
            END
        END
        
        #Verify the total number of records is the same as the database totals
        ${total_number_of_records_array}=             Split String    ${trimmed_total_number_of_records}       ${SPACE}
        ${total_number_of_records_array_length}=      Get Length    ${total_number_of_records_array}

        IF    ${total_number_of_records_array_length} == 2
            ${integer_validation_one}=       Common_Functions.Validate Integer    ${total_number_of_records_array[0]}
            IF    '${integer_validation_one}' == 'integer not valid'
                ${actual_total_number_of_records_data}=    Set Variable    ${total_number_of_records_array[1]}
            ELSE
                ${actual_total_number_of_records_data}=    Set Variable    ${total_number_of_records_array[0]}
            END
        ELSE
            ${integer_validation_two}=       Common_Functions.Validate Integer    ${total_number_of_records_array[0]}
            IF    '${integer_validation_two}' == 'integer not valid'
                ${actual_total_number_of_records_data}=    Set Variable    ${total_number_of_records_array[1]}
            ELSE
                ${actual_total_number_of_records_data}=    Set Variable    ${total_number_of_records_array[2]}
            END
        END

        ${actual_total_number_of_records_data_int}=     Convert To Integer    ${actual_total_number_of_records_data}
        ${db_total_users_num}=      Get the total number of VMS users from the database

        common_keywords.Verify if values are equal      ${db_total_users_num}       ${actual_total_number_of_records_data_int}

        Log    ${actual_start_record_data}
        Log   ${actual_end_record_data}
        Log   ${actual_total_number_of_records_data}


        SeleniumLibrary.Element Should Be Visible    ${VMS_USERS_TABLE}     #Verify that the 'VMS Added Users' Table is displayed

        #Verify that number of records displayed on every page corresponds with the page number details text
        ${table_tbody_element}=                 Catenate    ${VMS_USERS_TABLE}/tbody[contains(@class,'gs-table-body')]
        ${table_rows_element}=          Catenate    ${table_tbody_element}/tr
        ${rows}=    Get WebElements    ${table_rows_element}
        ${table_rows_count}=    Get Length      ${rows}

        ${actual_start_record_int}=     Convert To Integer    ${actual_start_record_data}
        ${actual_end_record_int}=     Convert To Integer    ${actual_end_record_data}
        ${actual_records_count_on_page}=    Evaluate    ${actual_end_record_int} - ${actual_start_record_int} + 1

        Should Be Equal As Strings    ${table_rows_count}    ${actual_records_count_on_page}



        #Verify that the next page displays the correct information in terms of page numbers
        SeleniumLibrary.Element Should Be Visible    ${USER_MANAGEMENT_BUTTONS_DIV}

        ${button_elements_locator}=         Catenate    ${USER_MANAGEMENT_BUTTONS_DIV}/button

        #Get all buttons that are currently displayed on the page
        ${button_elements}=     SeleniumLibrary.Get WebElements    ${button_elements_locator}
        ${total_number_of_buttons}=    Get Length    ${button_elements}

        ${previous_page_element}=   Set Variable    ${button_elements[0]}
        ${prev_page_text}=     SeleniumLibrary.Get Text    ${previous_page_element}

        ${next_page_element}=    Get From List    ${button_elements}    -1
        ${next_page_text}=     SeleniumLibrary.Get Text    ${next_page_element}
        ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${next_page_element}    disabled

        Should Be Equal As Strings    ${prev_page_text}    Prev
        Should Be Equal As Strings    ${next_page_text}    Next

        IF    '${next_page_disabled_status}' == 'None'
                 #Click on the 'Next' button
                 SeleniumLibrary.Click Element    ${next_page_element}
             ELSE
                Should Be Equal As Strings    ${actual_end_record_data}    ${actual_total_number_of_records_data}
        END

        RETURN      ${next_page_disabled_status}



Get the VMS User details from the database
    [Arguments]     ${USER_NAME}

    #Verify that all parameters are supplied
     ${result1}=    Run Keyword And Return Status    Should Not Be Empty    ${USER_NAME.strip()}
     ${condition1}=    Set Variable    ${result1}
    Run Keyword If    ${condition1} == ${False}
    ...     Fail    Please make sure that parameter value is provided!

    ${username_data}=  Replace String      ${USER_NAME.strip()}       '     ''

    ${db_type}=   Set Variable   'MSSQL'

    ${vms_user_details_query}   Set Variable     ${SQL_VMS_USER_DETAILS}
    ${vms_user_details_query}=  Replace String      ${vms_user_details_query}       USER_NAME     ${username_data.strip()}


    ${data_base_vms_user_details}=      Execute SQL Query  ${db_type}  ${vms_user_details_query}    True
    RETURN      ${data_base_vms_user_details}


Get the total number of VMS users from the database

    ${db_type}=   Set Variable   'MSSQL'

    ${vms_user_details_query}   Set Variable     ${SQL_GET_TOTAL_NUMBER_OF_VMS_USERS}
    ${data_base_vms_user_details}=      Execute SQL Query  ${db_type}  ${vms_user_details_query}    True
    ${total_users_number}=    Get From Dictionary    ${data_base_vms_user_details}    total_users
    RETURN      ${total_users_number}



#*** Test Cases ***
#Test DB
#    Format Timestamp       2024-09-11 07:50:12.037



